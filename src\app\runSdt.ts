import Sdt from "@/app/sdt";

(async () => {
  try {
    const sdt = new Sdt();
    Cypress.sdt = await sdt.initialize();

    before(() => {
      Cypress.sdt.setup.before();
    });

    beforeEach(() => {
      Cypress.sdt.setup.beforeEach();
    });

    afterEach(() => {
      Cypress.sdt.setup.afterEach();
    });

    after(() => {
      Cypress.sdt.setup.after();
    });

    Cypress.sdt.run();
  } catch (error) {
    console.error("Failed to initialize SDT:", error.message);
    throw error;
  }
})();
