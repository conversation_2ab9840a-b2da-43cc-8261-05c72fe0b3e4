import Excel from "exceljs";
import fs from "fs-extra";
import h from "@/app/helpers/all";
import path from "path";

const workbook = new Excel.Workbook();
let sdtFile;
let results;
let resultsFolder;
let resultsFile;
let resultsWorksheet;
let testsSummaryWorksheet;
let stepsWorksheet;
let screenshotsWorksheet;
let headerCellStyle;
let titleCellStyle;
let subTitleCellStyle;
let stepUseCaseRowNumber;
let testsSummaryUseCaseRowNumber;
let errorScreenshots;

export default async function writeResults(data) {
  sdtFile = data.sdtFilePath;
  results = data.tests;
  resultsFolder = data.outputFolderName;
  resultsFile = data.outputFileName;
  console.log("resultsInput", results);

  errorScreenshots = fs
    .readdirSync(resultsFolder)
    .filter((file) => file.match(/error.*/))
    .sort();

  await addResultsSheets();

  await workbook.xlsx.writeFile(path.resolve(resultsFolder, resultsFile));
}

async function addResultsSheets() {
  await workbook.xlsx.readFile(sdtFile);
  let errorScreenshotIndex;
  prepareResultsSheets();

  let lastStepHasError;
  results.forEach((test) => {
    test["steps"].forEach((step, index) => {
      if (index === 0) {
        fillTestHeader(test);
        errorScreenshotIndex = 0;
        return;
      }
      fillStep(step);
      if (step.screenshotFilename) {
        addScreenshot(step.screenshotFilename);
      }
      if (step.hasError) {
        if (errorScreenshots.length) {
          addScreenshot(errorScreenshots[errorScreenshotIndex]);
          errorScreenshotIndex++;
        }
      }
      lastStepHasError = step.hasError;
    });

    if (test["steps"].at(-1).action !== "End Test") {
      fillStep({ label: "End Test", hasError: true });
      lastStepHasError = true;
    }

    if (lastStepHasError) {
      fillTestsSummary("Error", test);
      fillTestResult("Error", test);
    } else {
      fillTestsSummary("Ok", test);
      fillTestResult("Ok", test);
    }
    testsSummaryWorksheet.addRow().height = 25;
    stepsWorksheet.addRow().height = 25;
  });

  moveTestsResultsSheetsToWorkbookStart();
  workbook.views[0].firstSheet = 0;
  workbook.views[0].activeTab = 0;
}

function prepareResultsSheets() {
  getCellStyles();
  prepareResultsWorksheet();
  prepareTestsSummaryWorksheet();
  prepareTestsStepsWorksheet();
  prepareScreenshotsWorksheet();
}

function getCellStyles() {
  headerCellStyle = {
    font: {
      bold: true,
      size: 11,
      color: {
        theme: 1,
      },
      name: "Calibri",
      family: 2,
    },
    border: {
      left: {
        style: "medium",
        color: {
          theme: 0,
          tint: -0.3499862666707358,
        },
      },
      right: {
        style: "medium",
        color: {
          theme: 0,
          tint: -0.3499862666707358,
        },
      },
      top: {
        style: "medium",
        color: {
          theme: 0,
          tint: -0.3499862666707358,
        },
      },
      bottom: {
        style: "medium",
        color: {
          theme: 0,
          tint: -0.3499862666707358,
        },
      },
    },
    fill: {
      type: "pattern",
      pattern: "none",
    },
    alignment: {
      horizontal: "center",
      vertical: "middle",
    },
  };

  titleCellStyle = {
    font: {
      bold: true,
      size: 11,
      color: {
        theme: 1,
      },
      name: "Calibri",
      family: 2,
    },
    border: {},
    fill: {
      type: "pattern",
      pattern: "solid",
      fgColor: {
        theme: 0,
        tint: -0.1499984740745262,
      },
      bgColor: {
        indexed: 64,
      },
    },
    alignment: {
      horizontal: "left",
      vertical: "middle",
      wrapText: true,
    },
  };

  subTitleCellStyle = {
    alignment: {
      horizontal: "left",
      vertical: "middle",
    },
  };
}

function prepareResultsWorksheet() {
  const sheet = workbook.getWorksheet("Elements");
  if (sheet) {
    sheet.spliceColumns(7, 1); // F = 6
    sheet.spliceColumns(6, 1); // E = 5
    sheet.spliceColumns(5, 1); // D = 4
  }

  resultsWorksheet = workbook.addWorksheet(`Results`, {
    views: [{ showGridLines: false }],
  });
  resultsWorksheet.properties.tabColor = { argb: "00eaff" };
  resultsWorksheet.properties.defaultRowHeight = 25;
  resultsWorksheet.columns = [
    { width: 20 },
    { width: 8 },
    { width: 30 },
    { width: 120 },
    { width: 8 },
  ];
  const headerValues = ["Sheet", "Test", "Feature", "Use Case", "Result"];

  const style = {
    font: {
      bold: true,
      size: 11,
      color: {
        theme: 1,
      },
      name: "Calibri",
      family: 2,
    },
    fill: {
      type: "pattern",
      pattern: "solid",
      fgColor: {
        theme: 0,
        tint: -0.1499984740745262,
      },
      bgColor: {
        indexed: 64,
      },
    },
    border: cellBorder("medium", "8e8e8e"),
    alignment: { horizontal: "center", vertical: "middle", wrapText: true },
  };

  resultsWorksheet.getColumn(1).alignment = {
    horizontal: "left",
    vertical: "middle",
  };

  resultsWorksheet.getColumn(2).alignment = {
    horizontal: "center",
    vertical: "middle",
  };

  resultsWorksheet.getColumn(3).alignment = {
    horizontal: "left",
    vertical: "middle",
  };

  resultsWorksheet.getColumn(4).alignment = {
    horizontal: "left",
    vertical: "middle",
  };

  resultsWorksheet.getColumn(5).alignment = {
    horizontal: "center",
    vertical: "middle",
  };

  const row = resultsWorksheet.addRow(headerValues);
  row.height = 25;
  row.eachCell((cell) => (cell.style = style));
}

function prepareTestsSummaryWorksheet() {
  testsSummaryWorksheet = workbook.addWorksheet("Tests Summary", {
    views: [{ showGridLines: false }],
  });
  testsSummaryWorksheet.properties.tabColor = { argb: "00eaff" };
  testsSummaryWorksheet.properties.defaultRowHeight = 25;
  testsSummaryWorksheet.columns = [{ width: 20 }, { width: 100 }];
}

function prepareTestsStepsWorksheet() {
  stepsWorksheet = workbook.addWorksheet("Tests Steps", {
    views: [{ showGridLines: false }],
  });
  stepsWorksheet.properties.tabColor = { argb: "00eaff" };
  stepsWorksheet.properties.defaultRowHeight = 25;
  stepsWorksheet.columns = [
    { width: 20 },
    { width: 40 },
    { width: 4 },
    { width: 60 },
    { width: 10 },
    { width: 60 },
    { width: 60 },
  ];
}

function prepareScreenshotsWorksheet() {
  let foundScreenshots;
  foundScreenshots = errorScreenshots.length > 0;
  if (!foundScreenshots) {
    results.forEach((test) => {
      test["steps"].forEach((step) => {
        if (step.screenshotFilename) foundScreenshots = true;
      });
    });
  }
  if (!foundScreenshots) return;

  screenshotsWorksheet = workbook.addWorksheet(`Screenshots`, {
    views: [{ showGridLines: false }],
  });
  screenshotsWorksheet.properties.tabColor = { argb: "00eaff" };
  screenshotsWorksheet.columns = [{ width: 40 }, { width: 100 }, { width: 40 }];
}

function fillTestHeader(test) {
  fillTestHeaderRow(stepsWorksheet, test, "Sheet");
  fillTestHeaderRow(stepsWorksheet, test, "Test");
  fillTestHeaderRow(stepsWorksheet, test, "Feature");
  fillTestHeaderRow(stepsWorksheet, test, "Context");
  fillTestHeaderRow(stepsWorksheet, test, "Use Case");
  stepUseCaseRowNumber = stepsWorksheet.rowCount;
  fillTestHeaderRow(stepsWorksheet, test, "Expected Outcome");
  fillTestHeaderRow(stepsWorksheet, test, "Notes");

  let rowValues = ["", "", "", "STEP", "RESULT", "SCREENSHOT", "NOTES"];
  const row = stepsWorksheet.addRow(rowValues);
  row.height = 25;
  row.eachCell((cell, columnNumber) => {
    if (columnNumber > 3) {
      cell.style = headerCellStyle;
    }
  });
}

function fillStep(step) {
  step["label"] = step["label"].replace(/\n/g, " - ");
  const stepFullRowNumber = step["label"].match(/\(([^)]+)\)/)?.[1];
  const nestedLevel = stepFullRowNumber?.split(".").length - 1;
  if (nestedLevel) step["label"] = "     ".repeat(nestedLevel) + step["label"];

  if (step.title || step.subTitle) {
    const titleRow = stepsWorksheet.addRow();
    titleRow.height = 25;

    stepsWorksheet.mergeCells(titleRow._number, 3, titleRow._number, 7);
    const firstCell = stepsWorksheet.getCell(titleRow._number, 3);

    firstCell.value = step["label"];
    if (step.title) {
      firstCell.style = titleCellStyle;
    }
    if (step.subTitle) {
      firstCell.style = subTitleCellStyle;
    }
    firstCell.border = cellBorder("thin");
    return;
  }

  const row = [
    "",
    "",
    "",
    step["label"] ?? "",
    step["hasError"] ? "Error" : "Ok",
    "",
    step["notes"] ?? "",
  ];

  const itemRow = stepsWorksheet.addRow(row);
  itemRow.height = 25;
  itemRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
    if (colNumber > 3) {
      cell.alignment = {
        vertical: "middle",
      };
      cell.border = cellBorder("thin");
    }

    if (colNumber === 3 || colNumber === 5) {
      cell.alignment = {
        horizontal: "center",
        vertical: "middle",
      };
    }
  });
}

function fillTestsSummary(testResult, test) {
  fillTestHeaderRow(testsSummaryWorksheet, test, "Sheet");
  fillTestHeaderRow(testsSummaryWorksheet, test, "Test");
  fillTestHeaderRow(testsSummaryWorksheet, test, "Feature");
  fillTestHeaderRow(testsSummaryWorksheet, test, "Context");
  let row = fillTestHeaderRow(testsSummaryWorksheet, test, "Use Case");
  testsSummaryUseCaseRowNumber = testsSummaryWorksheet.rowCount;
  if (row?.getCell(2)) {
    row.getCell(2).value = {
      text: test.useCase,
      hyperlink: `#'Tests Steps'!B${stepUseCaseRowNumber}`,
    };
  }
  fillTestHeaderRow(testsSummaryWorksheet, test, "Expected Outcome");
  fillTestHeaderRow(testsSummaryWorksheet, test, "Notes");

  row = testsSummaryWorksheet.addRow(["Result", testResult]);
  row.height = 25;
  row.eachCell((cell) => {
    cell.style = titleCellStyle;
  });

  fillTestsSummaryTitles(test);
}

function fillTestsSummaryTitles(test) {
  let row;
  let firstStep = true;
  test["steps"].forEach((step) => {
    if (step.title) {
      if (firstStep) fillColumnTitle();
      firstStep = false;
      const title = step.title.replace("=====>", "");
      row = testsSummaryWorksheet.addRow(["", title]);
      row.height = 25;
      const cell = testsSummaryWorksheet.getCell(row._number, 2);
      cell.border = cellBorder("thin");
      cell.alignment = {
        vertical: "middle",
      };
    }
    if (step.subTitle) {
      if (firstStep) fillColumnTitle();
      firstStep = false;
      const subTitle = `     ${step.subTitle}`;
      row = testsSummaryWorksheet.addRow(["", subTitle]);
      row.height = 25;
      const cell = testsSummaryWorksheet.getCell(row._number, 2);
      cell.border = cellBorder("thin");
      cell.alignment = {
        vertical: "middle",
      };
    }
  });
}

function fillColumnTitle() {
  const row = testsSummaryWorksheet.addRow(["", "SUMMARY STEPS"]);
  row.height = 25;
  const cell = testsSummaryWorksheet.getCell(row._number, 2);
  cell.style = headerCellStyle;
  cell.alignment = {
    vertical: "middle",
    horizontal: "center",
  };
}

function fillTestResult(testResult, test) {
  const rowValues = [
    test.sheet,
    test.test,
    test.fetaure,
    test.useCase,
    testResult,
  ];
  const row = resultsWorksheet.addRow(rowValues);
  row.getCell(4).value = {
    text: test.useCase?.toString(),
    hyperlink: `#'Tests Summary'!B${testsSummaryUseCaseRowNumber}`,
  };
  row.eachCell({ includeEmpty: true }, (cell) => {
    cell.border = cellBorder("thin");
  });
  row.getCell(4).style.alignment.wrapText = true;
  row.height = getRowHeight(row);
}

function fillTestHeaderRow(worksheet, test, rowLabel) {
  const testField = h.toCamelCase(rowLabel);
  if (!test[testField]) return;

  let rowValues = [rowLabel, test[testField]];
  let row = worksheet.addRow(rowValues);
  row.eachCell((cell) => {
    cell.style = titleCellStyle;
    cell.font.bold = false;
    cell.style.alignment.wrapText = true;
  });
  if (worksheet === stepsWorksheet)
    worksheet.mergeCells(row._number, 2, row._number, 7);
  row.height = getRowHeight(row);

  return row;
}

function addScreenshot(screenshotFilename) {
  if (!fs.existsSync(`${resultsFolder}/${screenshotFilename}`)) {
    return;
  }

  const newRow = screenshotsWorksheet.addRow({});
  const rowNum = newRow.number - 1;

  newRow.height = 300;
  newRow.getCell(1).alignment = {
    vertical: "middle",
    horizontal: "center",
  };
  newRow.getCell(2).alignment = {
    vertical: "middle",
    horizontal: "center",
  };
  newRow.getCell(3).alignment = {
    vertical: "middle",
    horizontal: "center",
  };
  newRow.getCell(1).border = cellBorder("thick");
  newRow.getCell(2).border = cellBorder("thick");
  newRow.getCell(3).border = cellBorder("thick");

  newRow.getCell(1).value = {
    text: `Results Row: ${stepsWorksheet.lastRow.number}`,
    hyperlink: `#'Tests Steps'!D${stepsWorksheet.lastRow.number}`,
  };

  const imageId = workbook.addImage({
    filename: `${resultsFolder}/${screenshotFilename}`,
    extension: "png",
  });

  screenshotsWorksheet.addImage(imageId, {
    tl: { col: 1, row: rowNum },
    br: { col: 2, row: rowNum + 1 }
  });
  
  newRow.getCell(3).value = {
    text: screenshotFilename,
    hyperlink: `./${screenshotFilename}`,
  };
  newRow.getCell(3).font = {
    bold: true,
  };

  stepsWorksheet.lastRow.getCell(6).value = {
    text: screenshotFilename,
    hyperlink: `#Screenshots!B${newRow.number}`,
  };
}

function cellBorder(borderStyle, borderColor = "d3d3d3") {
  return {
    top: { style: borderStyle, color: { argb: borderColor } },
    left: { style: borderStyle, color: { argb: borderColor } },
    bottom: { style: borderStyle, color: { argb: borderColor } },
    right: { style: borderStyle, color: { argb: borderColor } },
  };
}

function moveTestsResultsSheetsToWorkbookStart() {
  workbook.worksheets.forEach((sheet) => {
    if (sheet.name === "Results") {
      sheet["orderNo"] = 0;
    } else if (sheet.name === "Tests Summary") {
      sheet["orderNo"] = 1;
    } else if (sheet.name === "Tests Steps") {
      sheet["orderNo"] = 2;
    } else if (sheet.name === "Screenshots") {
      sheet["orderNo"] = 3;
    } else {
      sheet["orderNo"] = sheet["orderNo"] + 4;
    }
  });
}

function getRowHeight(row) {
  let rowHeight = 30;
  row.eachCell((cell, _colNumber) => {
    if (cell.value) {
      const height = calculateRowHeight(cell);
      rowHeight = Math.max(rowHeight, height);
    }
  });
  return rowHeight;
}

function calculateRowHeight(cell) {
  let cellText;
  if (typeof cell.value === "object") cellText = cell.value.text?.toString();
  else cellText = cell.value?.toString();

  let linesNumber = 1;
  if (cellText) {
    linesNumber = cellText.split("\n").length;
    if (linesNumber === 1) {
      let charactersPerLine = 100;
      linesNumber = Math.ceil(cellText.length / charactersPerLine);
    }
  }
  return Math.max(25, linesNumber * 15);
}
