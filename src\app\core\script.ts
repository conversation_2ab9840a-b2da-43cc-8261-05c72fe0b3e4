import h from "@/app/helpers/all";

export default class Script {
  name;
  input;
  steps;

  constructor(step) {
    this.set(step.target, step.rowNumber);
  }

  set(scriptName, stepRowNumber) {
    let foundScript;

    if (scriptName.includes(".")) {
      const sheetName = scriptName.slice(scriptName.indexOf(".") + 1).trim();
      foundScript = Cypress.sdt.data.scripts[sheetName].find((script) =>
        h.compareNormalizedStrings(script["script"], scriptName)
      );
    }

    if (!foundScript && Cypress.sdt.data.scripts[scriptName]) {
      foundScript = Object.values(
        Cypress.sdt.data.scripts[scriptName]
      ).find((script) =>
        h.compareNormalizedStrings(script["script"], scriptName)
      );
    }

    if (!foundScript) {
      foundScript = Object.values(Cypress.sdt.data.scripts)
        .flat()
        .find((script) =>
          h.compareNormalizedStrings(script["script"], scriptName)
        );
    }

    if (!foundScript) throw new Error(`Script not found: ${scriptName}`);

    foundScript = structuredClone(foundScript);
    this.name = foundScript["script"];
    this.input = foundScript["input"];
    this.steps = foundScript["steps"].map((step) => {
      if (step.rowNumber) step.rowNumber = `${stepRowNumber}.${step.rowNumber}`;
      return step;
    });
  }

  replaceParams(values) {
    const restoredValues = values.map((value) =>
      h.restoreQuotedStringsAndEscapedCharacters(value)
    );

    const paramsArgs = this.input
      ?.split(",")
      .map((param, index) => [param.trim(), restoredValues[index]?.trim()])
      .filter((paramArg) => paramArg[1]);

    this.steps = this.steps.map((step) => {
      paramsArgs?.forEach((paramArg) => {
        let regexPattern = new RegExp(paramArg[0].replace("$", "\\$"), "ig");
        if (step.target)
          step.target = step.target.replace(regexPattern, paramArg[1]);
        if (step.values)
          step.values = step.values.replace(regexPattern, paramArg[1]);
      });
      return step;
    });
  }
}
