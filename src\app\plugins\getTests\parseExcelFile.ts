import * as Excel from "exceljs";

import getSheetTable from "@/app/plugins/getTests/table";
import getSheetTestsOrScripts from "@/app/plugins/getTests/testOrScript";
import h from "@/app/helpers/all";

export default async function parseExcelFile(excelFile) {
  let data = {};

  data["workbook"] = new Excel.Workbook();
  await data["workbook"].xlsx.readFile(excelFile);

  getSheets(data);
  parseTests(data);
  parseScripts(data);
  parseTables(data);

  const convertedTables = convertTablesArraysToObjects(data["tables"]);

  const parsedData = {
    tests: [...data["tests"]  ],
    scripts: [...data["scripts"]],
    tables: convertedTables,
  };

  const elements = getElements(data);
  if (elements) parsedData["elements"] = elements;

  const aliases = getAliases(data);
  if (aliases) parsedData["aliases"] = aliases;

  return parsedData;
}

function getSheets(data) {
  data.sheets = data.workbook.worksheets.map((worksheet) => {
    return [worksheet.name, getSheetValues(worksheet)];
  });
}

function getSheetValues(worksheet) {
  const sheet = [];
  worksheet.eachRow((row, rowNumber) => {
    const sheetRow = [];
    row.eachCell({ includeEmpty: true }, (_column, columnNumber) => {
      sheetRow.push(
        worksheet.getCell(rowNumber, columnNumber).value?.toString()
      );
    });
    sheetRow.push(rowNumber);
    sheet.push(sheetRow);
  });
  return sheet;
}

function parseTests(data) {
  data.tests = data.sheets.reduce((tests, [sheetName, sheetContent]) => {
    const sheetTests = getSheetTestsOrScripts(sheetName, sheetContent, "test");
    tests = [...tests, ...sheetTests];
    return tests;
  }, []);
}

function parseScripts(data) {
  data.scripts = data.sheets.reduce(
    (scripts, [sheetName, sheetContent]) => {
      const sheetScripts = getSheetTestsOrScripts(
        sheetName,
        sheetContent,
        "script"
      );
      scripts = [...scripts, ...sheetScripts];
      return scripts;
    },
    []
  );
}

function parseTables(data) {
  data.tables = data.sheets.reduce((tables, [sheetName, sheetContent]) => {
    const sheetTable = getSheetTable(sheetName, sheetContent);
    sheetName = sheetName
      .replace(/ table/gi, "")
      .replace(/ data/gi, "")
      .trim();
    if (sheetTable) tables[sheetName] = sheetTable;
    return tables;
  }, {});
}

function getElements(data) {
  const elementsSheetIndex = data.sheets.findIndex(sheet => sheet[0] === "Elements");
  if (elementsSheetIndex < 0) return null;
  const elementsTable = data.sheets[elementsSheetIndex][1];    
  if (!elementsTable) return null;

  const elements = elementsTable
    .filter(
      (row) =>
        row[0].toLowerCase().replace(/ */g, "") !==
          "Item".toLowerCase().replace(/ */g, "") && row[0] !== row[1]
    )
    .reduce((result, item) => {
      const itemName = item[0].replace("{", "").replace("}", "");
      const itemToAdd = {};
      if (item[1]) itemToAdd["selector"] = item[1];
      if (item[2]) itemToAdd["root"] = item[2];
      if (item[3]) itemToAdd["locator"] = item[3];
      return {
        ...result,
        ...{ [itemName]: itemToAdd },
      };
    }, {});

  return elements;
}

function getAliases(data) {
  const aliasesSheetIndex = data.sheets.findIndex(sheet => sheet[0] === "Aliases");
  if (aliasesSheetIndex < 0) return null;
  const aliasesTable = data.sheets[aliasesSheetIndex][1];    
  if (!aliasesTable) return null;

  const aliases = aliasesTable
    .filter(
      (row) =>
        row[0].toLowerCase().replace(/ */g, "") !==
          "Alias Name".toLowerCase().replace(/ */g, "") && row[0] !== row[1]
    )
    .reduce((result, alias) => {
      const aliasName = alias[0].replace(/^«|»$/g, "");
      const aliasContent = alias[1];
      return {
        ...result,
        ...{
          [aliasName]: aliasContent,
        },
      };
    }, {});

  return aliases;
}

function convertTablesArraysToObjects(tables) {
  const convertedTablesEntries = Object.entries(tables).map(
    ([tableName, tableArray]) => {
      const convertedTableArray = convertTableArray(tableArray);
      return [tableName, convertedTableArray];
    }
  );

  const convertedTables = Object.fromEntries(convertedTablesEntries);

  return convertedTables;

  function convertTableArray(tableArray) {
    const convertedTableArray = tableArray.reduce((result, row) => {
      const rowId = h.getObjectFieldValueWithNormalizedKey(
        row,
        "id"
      );
      result[rowId] = { ...row };
      return result;
    }, {});
    return convertedTableArray;
  }
}
