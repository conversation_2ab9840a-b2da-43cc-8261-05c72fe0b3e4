import Entry from "@/app/core/entry";
import Screenshot from "@/app/core/screenshot";
import Target from "@/app/core/target";
import cypressHelper from "@/app/helpers/cypress";
import h from "@/app/helpers/all";

export default class Step {
  do;
  action;
  target;
  targetObject;
  values;
  simpleValues = [];
  namedValues = [];
  label;
  rowNumber;
  icon;
  screenshot;
  screenshotFilename;
  title;
  subTitle;
  hasError;
  rowNumberLabel = "";

  constructor(step) {
    Object.assign(this, step);

  }

  run() {
    return this.log().then(() => {
      if (
        this.title &&
        Cypress.sdt.config.takeScreenshotOnTitle?.toLowerCase().trim() ===
          "yes"
      ) {
        Screenshot.takeScreenshot();
      }
      if (["x", "z"].includes(this.do)) {
        this.hasError = true;
        this.setValues();
        this.setTarget();
        cy.then(() => this.runAction())
          .then(() => {
            if (this.screenshot) Screenshot.takeScreenshot();
          })
          .then(() => {
            if (this.do === "z") {
              Cypress.sdt.results.notCompletedTests.push(
                Cypress.sdt.current.test
              );
              throw new Error(`Test not completed`);
            }
          })
          .then(() => (this.hasError = false));
      }
      if (this.do === "") {
        Cypress.sdt.results.omittedSteps.push({
          ...this,
          test: Cypress.sdt.current.test,
        });
      }
    });
  }

  log() {
    this.setLabel();
    if (["Start Test", "End Test"].includes(this.action)) {
      return cypressHelper.logTitle(this.label, h.titleLevel3);
    }
    return cypressHelper.logTitle(this.label, h.titleLevel1, this.do);
  }

  setLabel() {
    if (
      this.action?.startsWith("Start Test") ||
      this.action?.startsWith("End Test")
    ) {
      this.label = this.action;
      return;
    }

    const lastIndex = this.rowNumberLabel?.lastIndexOf(".") ?? -1;
    this.rowNumberLabel =
      lastIndex === -1
        ? `${this.rowNumber}`
        : `${this.rowNumberLabel?.substring(0, lastIndex)}.${this.rowNumber}`;

    if (
      this.action?.startsWith("Start Script") ||
      this.action?.startsWith("End Script")
    ) {
      this.label = `(${this.rowNumberLabel}) ${this.action}`;
    }

    if (this.title) {
      this.label = `(${this.rowNumberLabel}) ${this.title}`;
      return;
    }
    if (this.subTitle) {
      this.label = `(${this.rowNumberLabel}) ${this.subTitle}`;
      return;
    }
    const baseLabel = `(${this.rowNumberLabel}) ${this.action}`;
    const targetInfo = this.target
      ? `\nTarget: ${this.target.replace(/\n/g, " ")}`
      : "";
    const valuesInfo = this.values
      ? `\nValues: ${JSON.stringify(this.values)?.replace(/\n/g, " ")}`
      : "";

    this.label = `${baseLabel}${targetInfo}${valuesInfo}`;
  }

  setTarget() {
    let isPartial = false;
    if (/partial/i.test(this.action)) {
      isPartial = true;
      this.action = this.action.replace(/partial/i, "").trim();
    }
    this.targetObject = new Target(this.target, isPartial);
  }

  setValues() {
    if (!this.values) return;
    this.values = h.replaceEscapedCharactersAndQuotedStrings(this.values);
    const entry = new Entry(this.values, true, Cypress.sdt.primitives);
    const expandedEntry = entry.expandedEntry;
    expandedEntry.forEach((value) => {
      if (typeof value == "string") {
        if (value.includes(":")) {
          const index = value.indexOf(":");
          let left = value.slice(0, index).replace(/"/g, "").trim();
          left = h.parseQuotedStringsAndEscapedCharacters(left);
          let right = value
            .slice(index + 1)
            .replace(/"/g, "")
            .trim();
          right = h.parseQuotedStringsAndEscapedCharacters(right);
          const namedValue = { left, right };
          this.namedValues.push(namedValue);
        } else
          this.simpleValues.push(
            h.parseQuotedStringsAndEscapedCharacters(value)
          );
      } else this.simpleValues.push(value);
    });
    this.setIcon();
  }

  setIcon() {
    this.icon = {};
    const leftIcon = this.namedValues?.filter((namedValue) =>
      h.compareNormalizedStrings(namedValue.left, "Left Icon")
    )[0]?.right;
    if (leftIcon) this.icon = { ...this.icon, left: leftIcon };

    const rightIcon = this.namedValues?.filter((namedValue) =>
      h.compareNormalizedStrings(namedValue.left, "Right Icon")
    )[0]?.right;
    if (rightIcon) this.icon = { ...this.icon, right: rightIcon };

    this.namedValues = this.namedValues.filter(
      (namedValue) =>
        !h.compareNormalizedStrings(namedValue.left, "Left Icon") &&
        !h.compareNormalizedStrings(namedValue.left, "Right Icon")
    );
  }

  runAction() {
    if (
      !this.action ||
      this.action.startsWith("Start Test") ||
      this.action.startsWith("End Test") ||
      this.action.startsWith("Start Script") ||
      this.action.startsWith("End Script")
    ) {
      this.hasError = false;
      return;
    }

    return cy
      .then(() => {
        const actionKey = h.getObjectSimilarKey(
          Cypress.sdt.actions,
          this.action
        );
        if (!actionKey) {
          throw new Error(`Action not found: ${this.action}`);
        }
        Cypress.sdt.actions[actionKey]();
      })
      .then(() => (this.hasError = false));
  }
}
