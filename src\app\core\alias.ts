import h from "@/app/helpers/all";

export default function parseLabelAliases(label, aliases,usedAliases) {
  return label.replace(/«([^»]+?)»/g, (alias) => {
    const argsBlock = alias.match(/\(.*[\s\S]*?\)/)?.[0];
    const args = argsBlock
      ?.replace(/^\((.*)\)$/, "$1")
      .split(",")
      .map((arg) => h.parseQuotedStringsAndEscapedCharacters(arg));

    let aliasLabel = alias
      .replace(/^«/, "")
      .replace(/»$/, "")
      .replace(argsBlock, "")
      .trim();
    let foundAlias = h.getObjectFieldValueWithNormalizedKey(aliases, aliasLabel);
    if (!usedAliases?.includes(aliasLabel)) {
      usedAliases.push(aliasLabel);
      usedAliases.sort();
    }

    let index = 0;
    foundAlias = foundAlias
      ?.replace(/\$[\w\d]+/g, () => {
        const replacement = args?.[index] ?? "";
        index++;
        return replacement.replace(/†/g, ",");
      })
      .replace(/\#\#/, "")
      .trim();
      foundAlias = foundAlias?.replace(
      /«(.*?)»/g,
      (nestedAliasLabel) => parseLabelAliases(nestedAliasLabel, aliases, usedAliases)
    );
    
    return foundAlias;
  });
}
